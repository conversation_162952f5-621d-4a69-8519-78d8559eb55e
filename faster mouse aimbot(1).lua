local ui_aimbot_enabled = ui.new_checkbox("Scripts", "Elements", "Enable Aimbot")
local ui_aimbot_key = ui.new_hotkey("Scripts", "Elements", "Aimbot Key", 0x05, 1, true, "Enable Aimbot")
local ui_aimbot_sticky = ui.new_checkbox("Scripts", "Elements", "Sticky Aim")
local ui_aimbot_target_mode = ui.new_dropdown("Scripts", "Elements", "Target Mode", {"Head", "Torso", "Both"})

local ui_aim_checks_dropdown = ui.new_multiselect("Scripts", "Elements", "Aim Checks", {
    "Team Check",
    "Health Check",
    "DH Knockcheck"
})

local ui_show_target_info = ui.new_checkbox("Scripts", "Elements", "Target Info", true)
local ui_target_viz_enabled = ui.new_checkbox("Scripts", "Elements", "Target Indicator", true)
local ui_target_viz_color = ui.new_colorpicker("Scripts", "Elements", "Target Viz Color", 159, 152, 222, 255, true)

local ui_fov_filled = ui.new_checkbox("Scripts", "Elements", "Filled FOV Circle")
local ui_fov_fill_color = ui.new_colorpicker("Scripts", "Elements", "Filled FOV Color", 218, 173, 245, 50, true)
local ui_fov_circle_outline_enabled = ui.new_checkbox("Scripts", "Elements", "FOV Outline")
local ui_fov_circle_outline_color = ui.new_colorpicker("Scripts", "Elements", "FOV Circle Outline Color", 255, 255, 255, 200, true)

local ui_aimbot_max_distance = ui.new_slider_int("Scripts", "Elements", "Max Distance", 10, 2000, 500)
local ui_aimbot_intensity = ui.new_slider_int("Scripts", "Elements", "Aim Intensity", 1, 330, 100)
local ui_fov_radius = ui.new_slider_int("Scripts", "Elements", "FOV Radius", 20, 300, 75)

local TARGET_VIZ_PULSE_DURATION = 1.0
local TARGET_VIZ_3D_MAX_RADIUS = 2.3
local INNER_CIRCLE_SCALE_FACTOR = 0.5
local INNER_CIRCLE_VERTICAL_OFFSET = 0.2
local TARGET_VIZ_LINE_THICKNESS = 2
local TARGET_VIZ_CENTER_MARKER_SIZE = 0
local GROUND_RING_Y_OFFSET = 3.0

local LED_BORDER_ANIM_SPEED = 150
local SNAKE_BODY_PIXEL_LENGTH = 150
local LED_BORDER_THICKNESS = 2
local SNAKE_HEAD_COLOR = {r = 159, g = 152, b = 222, a = 255}
local SNAKE_TAIL_COLOR = {r = 15, g = 15, b = 15, a = 235}

local MAX_AIMBOT_FRAME_DELTA = 600
local AIM_SMOOTHING_ALPHA = 0.6

local locked_target = nil
local current_bone_target = "Head"
local current_frame_target_data = nil
local last_target_screen_pos = nil
local last_target_world_pos = nil
local last_update_time = 0
local mouse_override_active = false
local smoothed_mx = 0
local smoothed_my = 0
local g_player_aimed_at_last_frame = nil
local led_border_anim_offset = 0

local function is_safety_check_enabled(check_name_to_find)
    local selected_checks = ui.get(ui_aim_checks_dropdown)
    if type(selected_checks) == "table" then
        for _, selected_name in pairs(selected_checks) do
            if selected_name == check_name_to_find then
                return true
            end
        end
    end
    return false
end

local function extract_color(ui_element, default_r, default_g, default_b, default_a)
    local color_table = ui.get(ui_element)
    local r, g, b, a
    if type(color_table) == "table" then
        if color_table.r ~= nil then
            r = math.floor(tonumber(color_table.r) or default_r)
            g = math.floor(tonumber(color_table.g) or default_g)
            b = math.floor(tonumber(color_table.b) or default_b)
            a = math.floor(tonumber(color_table.a) or default_a)
        else
            r = math.floor(tonumber(color_table[1]) or default_r)
            g = math.floor(tonumber(color_table[2]) or default_g)
            b = math.floor(tonumber(color_table[3]) or default_b)
            a = math.floor(tonumber(color_table[4]) or default_a)
        end
    else
        r, g, b, a = default_r, default_g, default_b, default_a
    end
    return r, g, b, a
end


local function get_current_bone_target(player, use_distance_check, target_mode_cached, cursor_pos_cached)
    local mode = target_mode_cached or ui.get(ui_aimbot_target_mode)

    if mode == "Head" then return "Head" end
    if mode == "Torso" then return "Chest" end

    
    if mode == "Both" and use_distance_check and player and player:is_alive() then
        local cur_x, cur_y
        if cursor_pos_cached and type(cursor_pos_cached.x) == "number" and type(cursor_pos_cached.y) == "number" then
            cur_x = cursor_pos_cached.x
            cur_y = cursor_pos_cached.y
        else
            cur_x, cur_y = input.cursor_position()
        end

        if type(cur_x) ~= "number" or type(cur_y) ~= "number" then
            return current_bone_target
        end
        
        local cursor_vec = vector(cur_x, cur_y, 0)
        
        local bones_to_check = {
            {name = "Head", pos_3d = vector(player:bone_position("Head")), dist_sq = math.huge, sx = nil, sy = nil, vis = false},
            {name = "Chest", pos_3d = vector(player:bone_position("Chest")), dist_sq = math.huge, sx = nil, sy = nil, vis = false},
            {name = "HumanoidRootPart", pos_3d = vector(player:bone_position("HumanoidRootPart")), dist_sq = math.huge, sx = nil, sy = nil, vis = false}
        }

        for _, bone_data in ipairs(bones_to_check) do
            if bone_data.pos_3d and type(bone_data.pos_3d.x) == "number" and not bone_data.pos_3d:is_zero() then
                local s_coords = {utility.world_to_screen(bone_data.pos_3d:unpack())}
                bone_data.sx, bone_data.sy = s_coords[1], s_coords[2]
                if bone_data.sx then 
                    bone_data.vis = true
                    bone_data.dist_sq = (vector(bone_data.sx, bone_data.sy, 0) - cursor_vec):length_sqr()
                end
            end
        end

        local visible_bones = {}
        for _, bone_data in ipairs(bones_to_check) do
            if bone_data.vis then
                table.insert(visible_bones, bone_data)
            end
        end

        if #visible_bones > 0 then
            table.sort(visible_bones, function(a, b) return a.dist_sq < b.dist_sq end)
            return visible_bones[1].name
        else
            
            
            return current_bone_target
        end
    end

    
    return current_bone_target 
end


local function get_target_position(player, target_mode_cached, cursor_pos_cached)
    if not player or not player:is_alive() then return vector(0, 0, 0) end

    local bone_to_aim_at_name = nil
    local final_pos = vector(0, 0, 0)
    local bone_names_to_try = {}

    local mode = target_mode_cached or ui.get(ui_aimbot_target_mode)
    local initial_bone_preference = get_current_bone_target(player, true, mode, cursor_pos_cached)

    if mode == "Head" then
        bone_names_to_try = {"Head", "Chest", "HumanoidRootPart"}
    elseif mode == "Torso" then
        
        bone_names_to_try = {"Chest", "HumanoidRootPart", "Head"}
    elseif mode == "Both" then
        
        
        bone_names_to_try = {initial_bone_preference}
        local fallback_order = {"Head", "Chest", "HumanoidRootPart"} 
        for _, bn in ipairs(fallback_order) do
            local found = false
            for _, existing_bn in ipairs(bone_names_to_try) do
                if existing_bn == bn then
                    found = true
                    break
                end
            end
            if not found then
                table.insert(bone_names_to_try, bn)
            end
        end
    else 
        bone_names_to_try = {"Head", "Chest", "HumanoidRootPart"} 
    end
    
    for _, bone_name in ipairs(bone_names_to_try) do
        if bone_name then 
            local pos_candidate = vector(player:bone_position(bone_name))
            if pos_candidate and type(pos_candidate.x) == "number" and not pos_candidate:is_zero() then
                final_pos = pos_candidate
                bone_to_aim_at_name = bone_name
                break 
            end
        end
    end

    if bone_to_aim_at_name then
        current_bone_target = bone_to_aim_at_name 
        return final_pos
    else
        
        
        return vector(0, 0, 0)
    end
end

local function get_local_player_position()
    local local_player = game.get_local_player()
    if local_player then
        local success_char, character = pcall(function() return local_player:get_character() end)
        if success_char and character then
            local hrp = character:find_first_child("HumanoidRootPart")
            if hrp then
                local success_pos, hrp_pos_table = pcall(function() return hrp:get_position() end)
                if success_pos and type(hrp_pos_table[1]) == "number" then
                    return vector(unpack(hrp_pos_table))
                end
            end
        end
    end
    local cam_pos_coords = {game.get_camera_pos()}
    if type(cam_pos_coords[1]) == "number" then
        return vector(unpack(cam_pos_coords))
    end
    utility.log("") 
    return vector(0, 0, 0)
end

local function get_best_target()
    if not ui.get(ui_aimbot_enabled) then
        if locked_target then locked_target = nil; last_target_world_pos = nil; last_target_screen_pos = nil; end
        return nil
    end
    if not ui.get(ui_aimbot_key) then
        if locked_target then locked_target = nil; last_target_world_pos = nil; last_target_screen_pos = nil; end
        return nil
    end
    
    local local_p_entity = entity.get_local_player()
    local local_team = local_p_entity and local_p_entity:get_team()
    local local_pos = get_local_player_position()
    if not local_pos or type(local_pos.x) ~= "number" then return nil end
    
    local use_team_check = is_safety_check_enabled("Team Check")
    local use_dh_knockcheck = is_safety_check_enabled("DH Knockcheck")

    local current_cursor_x, current_cursor_y = input.cursor_position()
    if type(current_cursor_x) ~= "number" or type(current_cursor_y) ~= "number" then
        return nil 
    end
    local current_cursor_vec = vector(current_cursor_x, current_cursor_y, 0) 

    local fov_radius_sq = ui.get(ui_fov_radius)^2
    local is_sticky_aim = ui.get(ui_aimbot_sticky)
    local target_mode = ui.get(ui_aimbot_target_mode) 
    local max_distance_sq = ui.get(ui_aimbot_max_distance)^2

    if is_sticky_aim and locked_target then
        -- With true sticky aim, we keep the target locked even if it becomes invalid
        -- Only try to get its position, but don't validate it strictly
        local sticky_target_bone_pos = get_target_position(locked_target, target_mode, current_cursor_vec)
        
        if sticky_target_bone_pos and type(sticky_target_bone_pos.x) == "number" then
            local screen_coords_sticky = {utility.world_to_screen(sticky_target_bone_pos:unpack())}
            local sx, sy = screen_coords_sticky[1], screen_coords_sticky[2]
            if sx and sy then
                last_target_world_pos = sticky_target_bone_pos
                last_target_screen_pos = vector(sx, sy, 0)
                last_update_time = utility.get_tickcount() / 1000
                return {player = locked_target, screen_pos = last_target_screen_pos, bone_pos = sticky_target_bone_pos, bone_name = current_bone_target}
            else
                -- Even if we can't get screen coordinates, we still keep the target locked
                -- but use the last known position
                if last_target_world_pos and last_target_screen_pos then
                    return {player = locked_target, screen_pos = last_target_screen_pos, bone_pos = last_target_world_pos, bone_name = current_bone_target}
                end
            end
        else
            -- Even if we can't get bone position, we still keep the target locked
            -- but use the last known position
            if last_target_world_pos and last_target_screen_pos then
                return {player = locked_target, screen_pos = last_target_screen_pos, bone_pos = last_target_world_pos, bone_name = current_bone_target}
            end
        end
    elseif not is_sticky_aim and locked_target then
        -- If sticky aim is disabled but we have a locked target, clear it
        locked_target = nil; last_target_world_pos = nil; last_target_screen_pos = nil;
    end

    local closest_distance_sq = math.huge
    local candidate_target_data = nil

    for _, player_entity in pairs(entity.get_players(false)) do
        if local_p_entity and player_entity == local_p_entity then goto continue_player_loop end
        
        local is_candidate_selectable = player_entity:is_alive()

        if is_candidate_selectable and use_dh_knockcheck and type(player_entity.get_health) == "function" then
            local health = player_entity:get_health()
            if type(health) == "number" and health <= 15 then
                is_candidate_selectable = false
            end
        end
        
        if not is_candidate_selectable then goto continue_player_loop end
        if not player_entity:is_visible() then goto continue_player_loop end 
        if use_team_check and local_team and player_entity:get_team() == local_team then goto continue_player_loop end
        
        local target_bone_3d_pos = get_target_position(player_entity, target_mode, current_cursor_vec)
        if not target_bone_3d_pos or type(target_bone_3d_pos.x) ~= "number" or target_bone_3d_pos:is_zero() then goto continue_player_loop end
        if (local_pos - target_bone_3d_pos):length_sqr() > max_distance_sq then goto continue_player_loop end
        
        local screen_coords_candidate = {utility.world_to_screen(target_bone_3d_pos:unpack())}
        local sx_cand, sy_cand = screen_coords_candidate[1], screen_coords_candidate[2]
        if not sx_cand then goto continue_player_loop end 
        
        local dist_to_cursor_sq = (sx_cand - current_cursor_x)^2 + (sy_cand - current_cursor_y)^2
        if dist_to_cursor_sq > fov_radius_sq then goto continue_player_loop end
        
        if dist_to_cursor_sq < closest_distance_sq then
            closest_distance_sq = dist_to_cursor_sq
            candidate_target_data = {
                player = player_entity, 
                screen_pos = vector(sx_cand, sy_cand, 0), 
                bone_pos = target_bone_3d_pos, 
                bone_name = current_bone_target 
            }
        end
        ::continue_player_loop::
    end
    
    if candidate_target_data then
        if is_sticky_aim then locked_target = candidate_target_data.player end
        last_target_world_pos = candidate_target_data.bone_pos
        last_target_screen_pos = candidate_target_data.screen_pos
        last_update_time = utility.get_tickcount() / 1000
        return candidate_target_data
    end

    return nil
end

local function aimbot_update()
    current_frame_target_data = get_best_target()
    g_player_aimed_at_last_frame = nil 

    local should_proceed_with_aiming = false

    if current_frame_target_data and current_frame_target_data.player then
        local target = current_frame_target_data.player
        local health = nil
        if type(target.get_health) == "function" then
            health = target:get_health()
        end

        local is_target_alive_check = true 
        if type(target.is_alive) == "function" then
            is_target_alive_check = target:is_alive()
        end

        if is_safety_check_enabled("Health Check") then
            if (type(health) == "number" and health <= 0) or not is_target_alive_check then
                if ui.get(ui_aimbot_enabled) then
                    ui.set(ui_aimbot_enabled, false) 
                end
                if locked_target == target then
                    locked_target = nil
                end
                current_frame_target_data = nil
                g_player_aimed_at_last_frame = nil
                should_proceed_with_aiming = false
                return 
            end
        end

        local passed_knock_check_validation = true
        if is_safety_check_enabled("DH Knockcheck") then 
            if not is_target_alive_check or (type(health) == "number" and health <= 15) then
                passed_knock_check_validation = false
            end
        end

        if passed_knock_check_validation then
            if current_frame_target_data.screen_pos and type(current_frame_target_data.screen_pos.x) == "number" then
                should_proceed_with_aiming = true
                g_player_aimed_at_last_frame = target
            else
                should_proceed_with_aiming = false
                current_frame_target_data = nil 
                g_player_aimed_at_last_frame = nil
                if locked_target == target then 
                    locked_target = nil
                end
            end
        else
            if locked_target == target then
                locked_target = nil 
            end
            current_frame_target_data = nil 
            g_player_aimed_at_last_frame = nil 
            should_proceed_with_aiming = false 
        end

    else 
        should_proceed_with_aiming = false
    end

    if not should_proceed_with_aiming or not current_frame_target_data then
        return 
    end

    local cursor_x, cursor_y = input.cursor_position()
    if type(cursor_x) ~= "number" then return end 

    local target_screen_x = current_frame_target_data.screen_pos.x
    local target_screen_y = current_frame_target_data.screen_pos.y
    local delta_x = target_screen_x - cursor_x
    local delta_y = target_screen_y - cursor_y

    local intensity_slider_val = ui.get(ui_aimbot_intensity)
    local actual_intensity = math.max(0.01, intensity_slider_val / 100.0)

    local move_x = delta_x * actual_intensity
    local move_y = delta_y * actual_intensity

    local current_move_magnitude_sq = move_x^2 + move_y^2
    local max_move_magnitude_sq = MAX_AIMBOT_FRAME_DELTA^2

    if current_move_magnitude_sq > max_move_magnitude_sq then
        if current_move_magnitude_sq > 0 then 
            local current_move_magnitude = math.sqrt(current_move_magnitude_sq)
            local scale = MAX_AIMBOT_FRAME_DELTA / current_move_magnitude
            move_x = move_x * scale
            move_y = move_y * scale
        else
            move_x, move_y = 0, 0 
        end
    end

    local screen_width, screen_height = cheat.get_window_size()
    if not screen_width or not screen_height then screen_width, screen_height = 1920, 1080 end 

    local potential_dest_x = cursor_x + move_x
    local potential_dest_y = cursor_y + move_y

    local clamped_final_dest_x = math.max(0, math.min(potential_dest_x, screen_width - 1))
    local clamped_final_dest_y = math.max(0, math.min(potential_dest_y, screen_height - 1))

    local final_move_x = clamped_final_dest_x - cursor_x
    local final_move_y = clamped_final_dest_y - cursor_y

    smoothed_mx = (final_move_x * AIM_SMOOTHING_ALPHA) + (smoothed_mx * (1.0 - AIM_SMOOTHING_ALPHA))
    smoothed_my = (final_move_y * AIM_SMOOTHING_ALPHA) + (smoothed_my * (1.0 - AIM_SMOOTHING_ALPHA))

    if math.abs(smoothed_mx) > 0.5 or math.abs(smoothed_my) > 0.5 then
        input.mouse_move(smoothed_mx, smoothed_my)
    end
end

local function aimbot_visuals()
    local is_key_active = ui.get(ui_aimbot_key)
    local win_w, win_h = cheat.get_window_size()
    if not win_w or not win_h then win_w, win_h = 1920, 1080 end

    if ui.get(ui_fov_filled) or ui.get(ui_fov_circle_outline_enabled) then
        local fov_center_x, fov_center_y = input.cursor_position()
        if type(fov_center_x) == "number" then
            local fov_val = ui.get(ui_fov_radius)
            if fov_val > 0 then
                if ui.get(ui_fov_filled) then
                    local r,g,b,a = extract_color(ui_fov_fill_color, 218, 173, 245, 50)
                    if a > 0 then render.circle(fov_center_x, fov_center_y, fov_val, r, g, b, a, 32) end
                end
                if ui.get(ui_fov_circle_outline_enabled) then
                    local r,g,b,a = extract_color(ui_fov_circle_outline_color, 255, 255, 255, 200)
                    if a > 0 then render.circle_outline(fov_center_x, fov_center_y, fov_val, r, g, b, a, 32) end
                end
            end
        end
    end

    if not ui.get(ui_aimbot_enabled) then
        return 
    end

    local visual_player_context = nil
    local use_dh_knockcheck_for_visuals = is_safety_check_enabled("DH Knockcheck")

    if is_key_active and g_player_aimed_at_last_frame and type(g_player_aimed_at_last_frame.is_alive) == "function" and g_player_aimed_at_last_frame:is_alive() then
        local show_this_visual = true
        if use_dh_knockcheck_for_visuals and type(g_player_aimed_at_last_frame.get_health) == "function" then
            local health = g_player_aimed_at_last_frame:get_health()
            if type(health) == "number" and health <= 15 then
                show_this_visual = false
            end
        end
        if show_this_visual then
            visual_player_context = g_player_aimed_at_last_frame
        end
    elseif is_key_active and current_frame_target_data and current_frame_target_data.player and current_frame_target_data.player:is_alive() then
        local show_this_visual = true
        if use_dh_knockcheck_for_visuals and type(current_frame_target_data.player.get_health) == "function" then
            local health = current_frame_target_data.player:get_health()
            if type(health) == "number" and health <= 15 then
                show_this_visual = false
            end
        end
        if show_this_visual then
            visual_player_context = current_frame_target_data.player
        end
    end

    if is_key_active and ui.get(ui_show_target_info) and visual_player_context then
        local target_player = visual_player_context
        local name = target_player:get_name() or "Unknown"
        local current_hp, max_hp = -1, 100

        if type(target_player.get_health) == "function" then
            local hp_val = target_player:get_health()
            if type(hp_val) == "number" then current_hp = math.floor(hp_val) end
        end
        if type(target_player.get_max_health) == "function" then
            local max_hp_val = target_player:get_max_health()
            if type(max_hp_val) == "number" and max_hp_val > 0 then max_hp = math.floor(max_hp_val) end
        end

        local health_string = ""
        if current_hp >= 0 then health_string = " [" .. current_hp .. "/" .. max_hp .. " HP]" end
        local full_text = name .. health_string

        local text_r, text_g, text_b = 255, 255, 255 
        local padding, font_idx, scale_text = 6, 1, true 

        local text_width, text_height = render.measure_text(font_idx, scale_text, full_text)
        local box_width, box_height = text_width + (padding * 2), text_height + (padding * 2)
        local box_x, box_y = (win_w / 2) - (box_width / 2), 15 

        render.rect(box_x, box_y, box_width, box_height, 30, 30, 40, 200, 0) 

        local current_time_sec = utility.get_tickcount() / 1000
        local perimeter = 2 * (box_width + box_height)
        if perimeter == 0 then perimeter = 1 end 

        led_border_anim_offset = (current_time_sec * LED_BORDER_ANIM_SPEED) % perimeter
        local snake_head_current_pos = led_border_anim_offset
        local snake_body_start_pos = (snake_head_current_pos - SNAKE_BODY_PIXEL_LENGTH + perimeter) % perimeter

        local border_points = {
            {x = box_x, y = box_y},
            {x = box_x + box_width, y = box_y},
            {x = box_x + box_width, y = box_y + box_height},
            {x = box_x, y = box_y + box_height}
        }
        local perimeter_traversed = 0

        for i = 1, 4 do
            local p1 = border_points[i]
            local p2 = border_points[i % 4 + 1] 
            local edge_dx, edge_dy = p2.x - p1.x, p2.y - p1.y
            local edge_length = math.sqrt(edge_dx^2 + edge_dy^2)
            if edge_length == 0 then goto next_border_edge end

            local step_size = 1 
            for dist_on_edge = 0, edge_length - step_size, step_size do
                local ratio_start = dist_on_edge / edge_length
                local ratio_end = math.min((dist_on_edge + step_size) / edge_length, 1.0)

                local segment_x1 = p1.x + edge_dx * ratio_start
                local segment_y1 = p1.y + edge_dy * ratio_start
                local segment_x2 = p1.x + edge_dx * ratio_end
                local segment_y2 = p1.y + edge_dy * ratio_end

                local segment_mid_pos_on_perimeter = perimeter_traversed + dist_on_edge + (step_size / 2)

                local line_r, line_g, line_b, line_a
                local is_in_snake_body = false

                if SNAKE_BODY_PIXEL_LENGTH > 0 then
                    if snake_body_start_pos <= snake_head_current_pos then 
                        if segment_mid_pos_on_perimeter > snake_body_start_pos and segment_mid_pos_on_perimeter <= snake_head_current_pos then
                            is_in_snake_body = true
                        end
                    else 
                        if segment_mid_pos_on_perimeter > snake_body_start_pos or segment_mid_pos_on_perimeter <= snake_head_current_pos then
                            is_in_snake_body = true
                        end
                    end
                end
                if SNAKE_BODY_PIXEL_LENGTH >= perimeter and perimeter > 0 then is_in_snake_body = true end 

                if is_in_snake_body then
                    local dist_from_head = (snake_head_current_pos - segment_mid_pos_on_perimeter + perimeter) % perimeter
                    local fade_percent = 0.0
                    if SNAKE_BODY_PIXEL_LENGTH > 0 then fade_percent = 1.0 - (dist_from_head / SNAKE_BODY_PIXEL_LENGTH) end
                    fade_percent = math.max(0.0, math.min(1.0, fade_percent)) 

                    line_a = math.floor(SNAKE_HEAD_COLOR.a * fade_percent)
                    line_r, line_g, line_b = SNAKE_HEAD_COLOR.r, SNAKE_HEAD_COLOR.g, SNAKE_HEAD_COLOR.b
                else
                    line_a = 0 
                    line_r, line_g, line_b = 0,0,0
                end

                if line_a > 0 then
                    render.line(segment_x1, segment_y1, segment_x2, segment_y2, line_r, line_g, line_b, line_a, LED_BORDER_THICKNESS)
                end
            end
            perimeter_traversed = perimeter_traversed + edge_length
            ::next_border_edge::
        end

        local text_render_x, text_render_y = box_x + padding, box_y + padding
        render.text(text_render_x, text_render_y, text_r, text_g, text_b, 255, font_idx, scale_text, name)
        if health_string ~= "" then
            local name_width, _ = render.measure_text(font_idx, scale_text, name)
            text_render_x = text_render_x + name_width
            render.text(text_render_x, text_render_y, text_r, text_g, text_b, 255, font_idx, scale_text, health_string)
        end
    end

    if ui.get(ui_target_viz_enabled) and is_key_active and visual_player_context then
        local hrp_pos_3d = vector(visual_player_context:bone_position("HumanoidRootPart")) 
        if hrp_pos_3d and type(hrp_pos_3d.x) == "number" and not hrp_pos_3d:is_zero() then
            local ring_base_x = hrp_pos_3d.x
            local ring_y_outer = hrp_pos_3d.y - GROUND_RING_Y_OFFSET
            local ring_y_inner = ring_y_outer - INNER_CIRCLE_VERTICAL_OFFSET
            local ring_base_z = hrp_pos_3d.z

            local viz_col_r, viz_col_g, viz_col_b, viz_base_alpha = extract_color(ui_target_viz_color, 159, 152, 222, 255)

            local pulse_duration_milli = TARGET_VIZ_PULSE_DURATION * 1000
            local max_radius_outer = TARGET_VIZ_3D_MAX_RADIUS
            local max_radius_inner = TARGET_VIZ_3D_MAX_RADIUS * INNER_CIRCLE_SCALE_FACTOR
            local line_thickness = TARGET_VIZ_LINE_THICKNESS

            local time_milli = utility.get_tickcount()
            local time_in_cycle = time_milli % pulse_duration_milli
            local pulse_progress = time_in_cycle / pulse_duration_milli 

            local current_radius_outer = max_radius_outer * pulse_progress
            local current_radius_inner = max_radius_inner * pulse_progress
            local current_alpha = math.floor(math.max(0, viz_base_alpha * (1.0 - pulse_progress))) 

            if current_alpha > 0 and line_thickness > 0 then
                local num_segments = 24 

                if current_radius_outer > 0.05 then 
                    local prev_screen_x_outer, prev_screen_y_outer = nil, nil
                    local first_screen_x_outer, first_screen_y_outer = nil, nil
                    for i = 0, num_segments do
                        local angle = (math.pi * 2 / num_segments) * i
                        local point_x_3d = ring_base_x + current_radius_outer * math.cos(angle)
                        local point_y_3d = ring_y_outer
                        local point_z_3d = ring_base_z + current_radius_outer * math.sin(angle)

                        local screen_coords = {utility.world_to_screen(point_x_3d, point_y_3d, point_z_3d)}
                        local sx, sy = screen_coords[1], screen_coords[2]

                        if sx and sy then
                            if prev_screen_x_outer then 
                                render.line(prev_screen_x_outer, prev_screen_y_outer, sx, sy, viz_col_r, viz_col_g, viz_col_b, current_alpha, line_thickness)
                            else 
                                first_screen_x_outer, first_screen_y_outer = sx, sy
                            end
                            prev_screen_x_outer, prev_screen_y_outer = sx, sy
                        else 
                            prev_screen_x_outer, prev_screen_y_outer = nil, nil 
                        end
                    end
                    if prev_screen_x_outer and first_screen_x_outer and num_segments > 0 then
                        if prev_screen_x_outer ~= first_screen_x_outer or prev_screen_y_outer ~= first_screen_y_outer then
                            render.line(prev_screen_x_outer, prev_screen_y_outer, first_screen_x_outer, first_screen_y_outer, viz_col_r, viz_col_g, viz_col_b, current_alpha, line_thickness)
                        end
                    end
                end

                if current_radius_inner > 0.05 then
                    local prev_screen_x_inner, prev_screen_y_inner = nil, nil
                    local first_screen_x_inner, first_screen_y_inner = nil, nil
                    for i = 0, num_segments do
                        local angle = (math.pi * 2 / num_segments) * i
                        local point_x_3d = ring_base_x + current_radius_inner * math.cos(angle)
                        local point_y_3d = ring_y_inner 
                        local point_z_3d = ring_base_z + current_radius_inner * math.sin(angle)

                        local screen_coords = {utility.world_to_screen(point_x_3d, point_y_3d, point_z_3d)}
                        local sx, sy = screen_coords[1], screen_coords[2]

                        if sx and sy then
                            if prev_screen_x_inner then
                                render.line(prev_screen_x_inner, prev_screen_y_inner, sx, sy, viz_col_r, viz_col_g, viz_col_b, current_alpha, line_thickness)
                            else
                                first_screen_x_inner, first_screen_y_inner = sx, sy
                            end
                            prev_screen_x_inner, prev_screen_y_inner = sx, sy
                        else
                            prev_screen_x_inner, prev_screen_y_inner = nil, nil
                        end
                    end
                    if prev_screen_x_inner and first_screen_x_inner and num_segments > 0 then
                         if prev_screen_x_inner ~= first_screen_x_inner or prev_screen_y_inner ~= first_screen_y_inner then
                            render.line(prev_screen_x_inner, prev_screen_y_inner, first_screen_x_inner, first_screen_y_inner, viz_col_r, viz_col_g, viz_col_b, current_alpha, line_thickness)
                        end
                    end
                end
            end
        end
    end
end

cheat.set_callback("update", aimbot_update)
cheat.set_callback("paint", aimbot_visuals)