local ui_aimbot_enabled = ui.new_checkbox("Scripts", "Elements", "Enable Aimbot")
local ui_aimbot_key = ui.new_hotkey("Scripts", "Elements", "Aimbot Key", 0x05, 1, true, "Enable Aimbot")
local ui_aimbot_sticky = ui.new_checkbox("Scripts", "Elements", "Sticky Aim")
local ui_aimbot_target_mode = ui.new_dropdown("Scripts", "Elements", "Target Mode", {"Head", "Torso", "Both"})

local ui_aim_checks_dropdown = ui.new_multiselect("Scripts", "Elements", "Aim Checks", {
    "Team Check",
    "Health Check",
    "DH Knockcheck"
})

local ui_fov_circle_outline_enabled = ui.new_checkbox("Scripts", "Elements", "FOV Outline")
local ui_fov_circle_outline_color = ui.new_colorpicker("Scripts", "Elements", "FOV Circle Outline Color", 255, 255, 255, 200, true)

local ui_aimbot_max_distance = ui.new_slider_int("Scripts", "Elements", "Max Distance", 10, 2000, 500)
local ui_aimbot_intensity = ui.new_slider_int("Scripts", "Elements", "Aim Intensity", 1, 330, 100)
local ui_fov_radius = ui.new_slider_int("Scripts", "Elements", "FOV Radius", 20, 300, 75)

local AimingMotion = {
    velocity = vector(0, 0, 0),
    stability = 0.5,
    fatigue = 0.0
}

local function clamp(val, min, max)
    return math.max(min, math.min(max, val))
end

local function lerp(a, b, t)
    return a + (b - a) * t
end

local function process_aiming_motion(delta, distance)
    local target_velocity = vector(delta.x, delta.y, 0)
    
    local smoothing_factor = 0.15
    if distance < 100 then
        smoothing_factor = 0.35 -- Wrist movement
    end

    AimingMotion.velocity = lerp(AimingMotion.velocity, target_velocity, smoothing_factor)

    -- Stabilization
    local stabilization_factor = 0.85
    local stabilized_delta = vector(AimingMotion.velocity.x * stabilization_factor, AimingMotion.velocity.y * stabilization_factor, 0)

    return stabilized_delta
end

local smoothed_x = 0
local smoothed_y = 0

local locked_target = nil
local current_bone_target = "Head"
local current_frame_target_data = nil
local last_target_screen_pos = nil
local last_target_world_pos = nil
local last_update_time = 0
local g_player_aimed_at_last_frame = nil

local function is_safety_check_enabled(check_name_to_find)
    local selected_checks = ui.get(ui_aim_checks_dropdown)
    if type(selected_checks) == "table" then
        for _, selected_name in pairs(selected_checks) do
            if selected_name == check_name_to_find then
                return true
            end
        end
    end
    return false
end

local function extract_color(ui_element, default_r, default_g, default_b, default_a)
    local color_table = ui.get(ui_element)
    local r, g, b, a
    if type(color_table) == "table" then
        if color_table.r ~= nil then
            r = math.floor(tonumber(color_table.r) or default_r)
            g = math.floor(tonumber(color_table.g) or default_g)
            b = math.floor(tonumber(color_table.b) or default_b)
            a = math.floor(tonumber(color_table.a) or default_a)
        else
            r = math.floor(tonumber(color_table[1]) or default_r)
            g = math.floor(tonumber(color_table[2]) or default_g)
            b = math.floor(tonumber(color_table[3]) or default_b)
            a = math.floor(tonumber(color_table[4]) or default_a)
        end
    else
        r, g, b, a = default_r, default_g, default_b, default_a
    end
    return r, g, b, a
end

local function get_current_bone_target(player, use_distance_check, target_mode_cached, cursor_pos_cached)
    local mode = target_mode_cached or ui.get(ui_aimbot_target_mode)

    if mode == "Head" then return "Head" end
    if mode == "Torso" then return "Chest" end

    if mode == "Both" and use_distance_check and player and player:is_alive() then
        local cur_x, cur_y
        if cursor_pos_cached and type(cursor_pos_cached.x) == "number" and type(cursor_pos_cached.y) == "number" then
            cur_x = cursor_pos_cached.x
            cur_y = cursor_pos_cached.y
        else
            cur_x, cur_y = input.cursor_position()
        end

        if type(cur_x) ~= "number" or type(cur_y) ~= "number" then
            return current_bone_target
        end
        
        local cursor_vec = vector(cur_x, cur_y, 0)
        
        local bones_to_check = {
            {name = "Head", pos_3d = vector(player:bone_position("Head")), dist_sq = math.huge, sx = nil, sy = nil, vis = false},
            {name = "Chest", pos_3d = vector(player:bone_position("Chest")), dist_sq = math.huge, sx = nil, sy = nil, vis = false},
            {name = "HumanoidRootPart", pos_3d = vector(player:bone_position("HumanoidRootPart")), dist_sq = math.huge, sx = nil, sy = nil, vis = false}
        }

        for _, bone_data in ipairs(bones_to_check) do
            if bone_data.pos_3d and type(bone_data.pos_3d.x) == "number" and not bone_data.pos_3d:is_zero() then
                local s_coords = {utility.world_to_screen(bone_data.pos_3d:unpack())}
                bone_data.sx, bone_data.sy = s_coords[1], s_coords[2]
                if bone_data.sx then 
                    bone_data.vis = true
                    bone_data.dist_sq = (vector(bone_data.sx, bone_data.sy, 0) - cursor_vec):length_sqr()
                end
            end
        end

        local visible_bones = {}
        for _, bone_data in ipairs(bones_to_check) do
            if bone_data.vis then
                table.insert(visible_bones, bone_data)
            end
        end

        if #visible_bones > 0 then
            table.sort(visible_bones, function(a, b) return a.dist_sq < b.dist_sq end)
            return visible_bones[1].name
        else
            return current_bone_target
        end
    end

    return current_bone_target 
end

local function get_target_position(player, target_mode_cached, cursor_pos_cached)
    if not player or not player:is_alive() then return vector(0, 0, 0) end

    local bone_to_aim_at_name = nil
    local final_pos = vector(0, 0, 0)
    local bone_names_to_try = {}

    local mode = target_mode_cached or ui.get(ui_aimbot_target_mode)
    local initial_bone_preference = get_current_bone_target(player, true, mode, cursor_pos_cached)

    if mode == "Head" then
        bone_names_to_try = {"Head", "Chest", "HumanoidRootPart"}
    elseif mode == "Torso" then
        bone_names_to_try = {"Chest", "HumanoidRootPart", "Head"}
    elseif mode == "Both" then
        bone_names_to_try = {initial_bone_preference}
        local fallback_order = {"Head", "Chest", "HumanoidRootPart"} 
        for _, bn in ipairs(fallback_order) do
            local found = false
            for _, existing_bn in ipairs(bone_names_to_try) do
                if existing_bn == bn then
                    found = true
                    break
                end
            end
            if not found then
                table.insert(bone_names_to_try, bn)
            end
        end
    else 
        bone_names_to_try = {"Head", "Chest", "HumanoidRootPart"} 
    end
    
    for _, bone_name in ipairs(bone_names_to_try) do
        if bone_name then 
            local pos_candidate = vector(player:bone_position(bone_name))
            if pos_candidate and type(pos_candidate.x) == "number" and not pos_candidate:is_zero() then
                final_pos = pos_candidate
                bone_to_aim_at_name = bone_name
                break 
            end
        end
    end

    if bone_to_aim_at_name then
        current_bone_target = bone_to_aim_at_name 
        return final_pos
    else
        return vector(0, 0, 0)
    end
end

local function get_local_player_position()
    local local_player = game.get_local_player()
    if local_player then
        local success_char, character = pcall(function() return local_player:get_character() end)
        if success_char and character then
            local hrp = character:find_first_child("HumanoidRootPart")
            if hrp then
                local success_pos, hrp_pos_table = pcall(function() return hrp:get_position() end)
                if success_pos and type(hrp_pos_table[1]) == "number" then
                    return vector(unpack(hrp_pos_table))
                end
            end
        end
    end
    local cam_pos_coords = {game.get_camera_pos()}
    if type(cam_pos_coords[1]) == "number" then
        return vector(unpack(cam_pos_coords))
    end
    return vector(0, 0, 0)
end

local function get_best_target()
    if not ui.get(ui_aimbot_enabled) or not ui.get(ui_aimbot_key) then
        locked_target = nil
        return nil
    end
    
    local local_p_entity = entity.get_local_player()
    local local_team = local_p_entity and local_p_entity:get_team()
    local local_pos = get_local_player_position()
    if not local_pos or type(local_pos.x) ~= "number" then return nil end
    
    local use_team_check = is_safety_check_enabled("Team Check")
    local use_dh_knockcheck = is_safety_check_enabled("DH Knockcheck")

    local current_cursor_x, current_cursor_y = input.cursor_position()
    if type(current_cursor_x) ~= "number" or type(current_cursor_y) ~= "number" then
        return nil 
    end
    local current_cursor_vec = vector(current_cursor_x, current_cursor_y, 0) 

    local fov_radius_sq = ui.get(ui_fov_radius)^2
    local is_sticky_aim = ui.get(ui_aimbot_sticky)
    local target_mode = ui.get(ui_aimbot_target_mode) 
    local max_distance_sq = ui.get(ui_aimbot_max_distance)^2

    if is_sticky_aim and locked_target then
        local is_sticky_target_valid = false
        local sticky_target_bone_pos = nil
        
        local sticky_target_ok_for_checks = locked_target:is_alive() and locked_target:is_visible()
        if sticky_target_ok_for_checks and use_team_check and local_team and locked_target:get_team() == local_team then
            sticky_target_ok_for_checks = false
        end

        if sticky_target_ok_for_checks and use_dh_knockcheck and type(locked_target.get_health) == "function" then
            local health = locked_target:get_health()
            if type(health) == "number" and health <= 15 then
                sticky_target_ok_for_checks = false
            end
        end

        if sticky_target_ok_for_checks then
            sticky_target_bone_pos = get_target_position(locked_target, target_mode, current_cursor_vec)
            if sticky_target_bone_pos and type(sticky_target_bone_pos.x) == "number" and not sticky_target_bone_pos:is_zero() then
                if (local_pos - sticky_target_bone_pos):length_sqr() <= max_distance_sq then
                    is_sticky_target_valid = true
                end
            end
        end

        if is_sticky_target_valid then
            local screen_coords_sticky = {utility.world_to_screen(sticky_target_bone_pos:unpack())}
            local sx, sy = screen_coords_sticky[1], screen_coords_sticky[2]
            if sx and sy then 
                return {player = locked_target, screen_pos = vector(sx, sy, 0), bone_pos = sticky_target_bone_pos, bone_name = current_bone_target}
            end
        end
        locked_target = nil
    end

    local closest_distance_sq = math.huge
    local candidate_target_data = nil

    for _, player_entity in pairs(entity.get_players(false)) do
        if local_p_entity and player_entity == local_p_entity then goto continue_player_loop end
        
        local is_candidate_selectable = player_entity:is_alive()

        if is_candidate_selectable and use_dh_knockcheck and type(player_entity.get_health) == "function" then
            local health = player_entity:get_health()
            if type(health) == "number" and health <= 15 then
                is_candidate_selectable = false
            end
        end
        
        if not is_candidate_selectable or not player_entity:is_visible() then goto continue_player_loop end 
        if use_team_check and local_team and player_entity:get_team() == local_team then goto continue_player_loop end
        
        local target_bone_3d_pos = get_target_position(player_entity, target_mode, current_cursor_vec)
        if not target_bone_3d_pos or type(target_bone_3d_pos.x) ~= "number" or target_bone_3d_pos:is_zero() then goto continue_player_loop end
        if (local_pos - target_bone_3d_pos):length_sqr() > max_distance_sq then goto continue_player_loop end
        
        local screen_coords_candidate = {utility.world_to_screen(target_bone_3d_pos:unpack())}
        local sx_cand, sy_cand = screen_coords_candidate[1], screen_coords_candidate[2]
        if not sx_cand then goto continue_player_loop end 
        
        local dist_to_cursor_sq = (sx_cand - current_cursor_x)^2 + (sy_cand - current_cursor_y)^2
        if dist_to_cursor_sq > fov_radius_sq then goto continue_player_loop end
        
        if dist_to_cursor_sq < closest_distance_sq then
            closest_distance_sq = dist_to_cursor_sq
            candidate_target_data = {
                player = player_entity, 
                screen_pos = vector(sx_cand, sy_cand, 0), 
                bone_pos = target_bone_3d_pos, 
                bone_name = current_bone_target 
            }
        end
        ::continue_player_loop::
    end
    
    if candidate_target_data then
        if is_sticky_aim then locked_target = candidate_target_data.player end
        return candidate_target_data
    end

    return nil
end

local function aimbot_update()
    current_frame_target_data = get_best_target()

    if not current_frame_target_data then return end

    local cursor_x, cursor_y = input.cursor_position()
    if type(cursor_x) ~= "number" then return end

    local target_screen_x = current_frame_target_data.screen_pos.x
    local target_screen_y = current_frame_target_data.screen_pos.y
    
    local intensity_slider_val = ui.get(ui_aimbot_intensity)
    local actual_intensity = math.max(0.01, intensity_slider_val / 100.0)

    smoothed_x = smoothed_x + (target_screen_x - smoothed_x) * actual_intensity
    smoothed_y = smoothed_y + (target_screen_y - smoothed_y) * actual_intensity

    local delta_x = smoothed_x - cursor_x
    local delta_y = smoothed_y - cursor_y

    local distance = math.sqrt(delta_x^2 + delta_y^2)
    local processed_delta = process_aiming_motion(vector(delta_x, delta_y, 0), distance)

    if processed_delta:length() > 0.5 then
        input.mouse_move(processed_delta.x, processed_delta.y)
    end
end

local function aimbot_visuals()
    if ui.get(ui_fov_circle_outline_enabled) then
        local fov_center_x, fov_center_y = input.cursor_position()
        if type(fov_center_x) == "number" then
            local fov_val = ui.get(ui_fov_radius)
            if fov_val > 0 then
                local r,g,b,a = extract_color(ui_fov_circle_outline_color, 255, 255, 255, 200)
                if a > 0 then render.circle_outline(fov_center_x, fov_center_y, fov_val, r, g, b, a, 32) end
            end
        end
    end
end

cheat.set_callback("update", aimbot_update)
cheat.set_callback("paint", aimbot_visuals)
