---
description: Interact with cheat functions.
---

# cheat

## Functions:

### Set Callback

```lua
cheat.set_callback(string: event, function()
end)
```

Executes a function on the specified event.

### Get Window Size

```lua
cheat.get_window_size()
```

Returns the size of the cheat overlay window.
---
description: Interact with menu.
---

# ui

{% hint style="info" %}
All elements created in your script, you should not reference. However, they do return the item reference required to interact with your elements. If you're trying to interact with an existing menu item, you must reference the element before accessing it.
{% endhint %}

## Functions:

### Get

```lua
ui.get(item: number)
```

For a checkbox, returns true or false. For a slider, returns an integer. For a hotkey, returns true if the hotkey is active. For a color picker, returns table with r, g, b, a. For a dropdown, returns name of selected option. For a multiselect, returns a table of selected option names.

### Set

```lua
ui.set(item: int, value: dynamic)
```

For checkboxes, pass true or false. For a slider, pass a number that is within the slider's minimum/maximum values. For color pickers, pass the arguments r, g, b, a in a table. For a dropdown, pass a name which is within the dropdowns options. For a multiselect pass a table of option names that are within the multiselects options.

### Checkbox

```lua
ui.new_checkbox(string: tab, string: container, string: name)
```

Creates a toggle in the menu, has the ability to return true/false as a value. Returns an item reference as a number, which can be used to interact with the element itself.

### Slider

Creates a slider in the menu, has the ability to return a number as a value. Returns an item reference as a number, which can be used to interact with the element itself.

```lua
ui.new_slider_int(string: tab, string: container, string: name, int: min, int: max, int: default)
```

### Slider Float

Creates a slider in the menu, has the ability to return a number but in decimal form as a value. Returns an item reference as a number, which can be used to interact with the element itself.

```lua
ui.new_slider_float(string: tab, string: container, string: name, float: min, float: max, float: default)
```

### Hotkey

Creates a hotkey in the menu, has the ability to return true/false as a value. Returns an item reference as a number, which can be used to interact with the element itself.

{% hint style="info" %}
Keys:

[https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)

Modes:

* 0 - Hold
* 1 - Toggle
* 2 - Always
{% endhint %}

```lua
ui.new_hotkey(string: tab, string: container, string: name, int: key, int: mode, bool: inline)
```

### Colorpicker

Creates a color picker in the menu, has the ability to return four numbers (r, g, b, a) as values. Returns an item reference as a number, which can be used to interact with the element itself.

```lua
ui.new_colorpicker(string: tab, string: container, string: name, int: r, int: g, int: b, int: a, bool: inline)
```

### Text Input

Creates a text input in the menu, has the ability to return a string as a value. Returns an item reference as a number, which can be used to interact with the element itself.

```lua
ui.new_input_text(string: tab, string: container, name: string)
```

### Dropdown

Creates a dropdown in the menu, has the ability to return a string as a value. Returns an item reference as a number, which can be used to interact with the element itself.

```lua
ui.new_dropdown(string: tab, string: container, name: string, table: options)
```

### Multiselect

Creates a multiselect dropdown in the menu, has the ability to return a table containing names of selected options. Returns an item reference as a number, which can be used to interact with the element itself.

```lua
ui.new_multiselect(string: tab, string: container, name: string, table: options)
```

### Button

```lua
ui.new_button(string: tab, string: container, name: string, function: callback)
```

Creates a new button in the menu, when pressed the function passed to the callback is triggered.

### Reference

```lua
ui.reference(string: tab, string: container, name: string)
```

Returns an item reference as a number, which can be used to interact with the element itself.

### Listbox

```lua
ui.new_listbox(string: tab, string: container, name: string, table: options)
```

Creates a listbox in the menu, has the ability to return the selected item index. Similar to the list that holds configuration preset names. Returns an item reference as a number, which can be used to interact with the element itself.

### Label

```lua
ui.new_label(string: tab, string: container, string: text)
```

Create a piece of text in the menu, does not have the ability to return anything. Returns an item reference as a number.

### Set Visible

```lua
ui.set_visible(number: item, status: boolean)
```

Sets the visibility of a menu item.

### Is Open

```lua
ui.is_open()
```

Returns true if the menu is currently open.
---
description: Interact with the world wide web.
---

# http

## Functions:

### Get

```lua
http.get(string: url, table: headers, function(body)
    -- Response received!
end)
```

Makes a HTTP request with the method GET.

### Post

```lua
http.post(string: url, table: headers, string: body, function(body)
    -- Response received!
end)
```

Makes a HTTP request with the method POST.
---
description: Interact with files.
---

# file

## Files location

Files belong in: `C:\Users\<USER>\AppData\Roaming\Melatonin\Roblox External\Scripts\files`

{% hint style="warning" %}
For security reasons, all File API related functions are restricted to one Folder. If you're trying to interact with files or folder outside of this directory it will not work.
{% endhint %}

## Functions:

### Read

```lua
file.read(string: path)
```

Returns the contents of that file as a string.

### Write

```lua
file.write(string: file, string: new_content)
```

If the file written does not exist, it will be created with the content. However, if the file does exist it will be overwritten with the content.
---
description: Interact with objects
---

# json

## Functions:

### Stringify

```lua
json.stringify(table: items)
```

Returns the contents of the table in a JSON formatted string.

### Parse

```lua
json.parse(string: json)
```

Returns the contents of your JSON formatted string as a table, so that the contents are easily accessible.
---
description: Interact with mathematical objects.
---

# vector

## Functions:

### Initialization

```lua
vector(float: x, float: y, float: z)
```

The constructor `vector(float x, float y, float z)` initializes a vector with the specified `x`, `y`, and `z`.

### Equal

```lua
vector:is_equal(vector)
```

Returns true if the current vector is equal to another vector.

### Zero

```lua
vector:is_zero()
```

Returns true if all values of the vector are zero.

### Scale

```lua
vector:scale(factor)
```

Scales the vector by a given factor and returns a new vector with the scaled values, without modifying the original vector.

### Length

```lua
vector:length()
```

Calculates and returns the length (magnitude) of the vector.

### Length 2D

```lua
vector:length_2d()
```

Calculates and returns the 2D length (magnitude) of the vector, using only the `x` and `y` components.

### Square Length

```lua
vector:length_sqr()
```

Calculates and returns the squared length (magnitude) of the vector, which is the sum of the squares of its components.

### Square Length 2D

```lua
vector:length_2f()
```

Calculates and returns the squared length of the vector in 2D space, using only the `x` and `y` components.

### Dot

```lua
vector:dot(vector)
```

Calculates and returns the dot product of the current vector with another vector.

### Cross

```lua
vector:cross(vector)
```

Computes and returns the cross product of the current vector with another vector.

### Distance

```lua
vector:dist_to(vector)
```

Calculates and returns the distance between the current vector and another vector.

### Unpack

```lua
vector:unpack()
```

Returns the vector's components (e.g., `x`, `y`, `z`)

### Normalize

```lua
vector:normalize()
```

Normalizes the vector (scales it to a unit length) and returns the result. If the vector has zero length, it returns the original vector unchanged.
---
description: Interact with the overlay and drawing.
---

# render

## Functions:

### Text

```lua
render.text(int: x, int: y, int: r, int: g, int: b, int: a, int: font, bool: scale, string: text)
```

### Line

```lua
render.line(int: x1, int: y1, int: x2, int: y2, int: r, int: g, int: b, int: a, int: thickness)
```

### Rectangle Outline

```lua
render.rect_outline(int: x, int: y, int: w, int: h, int: r, int: g, int: b, int: a, int: rounding)
```

### Rectangle

```lua
render.rect(int: x, int: y, int: w, int: h, int: r, int: g, int: b, int: a, int rounding)
```

### Circle Outline

```lua
render.circle_outline(int: x, int: y, int: radius, int: r, int: g, int: b, int: a, int: segments)
```

### Circle

```lua
render.circle(int: x, int: y, int: radius, int: r, int: g, int: b, int: a, int: segments)
```

### Triangle Outline

```lua
render.triangle_outline(int: x0, int: y0, int: x1, int: y1, int: x2, int: y2, int: r, int: g, int: b, int: a, float: thickness)
```

### Triangle

```lua
render.triangle(int: x0, int: y0, int: x1, int: y1, int: x2, int: y2, int: r, int: g, int: b, int: a)
```

### Gradient

```lua
render.gradient(int: x, int: y, int: w, int: h, int: r1, int: g1, int: b1, int: a1, int: r2, int: g2, int: b2, int: a2, bool: ltr)
```

### Measure Text

```lua
render.measure_text(int: font, bool: scale, string: text)
```

This will return the calculated width and height of the text.

### Image

```lua
render.image(int: texture, int: x, int: y, int: w, int: h, int: r, int: g, int: b, int: a)
```

---
description: Interact with cached entities, usually just player info in this case.
---

# entity

{% hint style="info" %}
When using the Entity API the info is returned from what the cheat has saved for it's own usage. It's safe to use these functions within the paint callback and experience little to no performance drawbacks. This should work across all games that are supported.
{% endhint %}

## Functions:

### Get Players

```lua
entity.get_players(boolean: only_enemies)
```

Returns a table of players. If `only_enemies` isn't passed it will default to false. The local player and dead players will be excluded.

### Get Parts

```lua
entity.get_parts()
```

Returns a table of all parts in the game. Refer to the methods below on how to read their information.

### Get Local Player

```lua
entity.get_local_player()
```

Returns the local player.

### Get Target

```lua
entity.get_target()
```

Returns the aimbot target.

### Get Position

```lua
player:get_position()
```

Returns the player positon as vector (x, y, z).

### Get Name

```lua
player:get_name()
```

Returns the player name as a string.

### Get Bounding Box

```lua
player:get_bbox()
```

Returns the player bounding box as x, y, w, h which are screen coordinates.

### Is Alive

```lua
player:is_alive()
```

Returns true if the player is alive.

### Is Enemy

```lua
player:is_enemy()
```

Returns true if the player is an enemy

### Is Visible

```lua
player:is_visible()
```

Returns true if the player is visible

### Is Whitelisted

```lua
player:is_whitelisted()
```

Returns true if the player is whitelisted.

### Get Health

```lua
player:get_health();
```

Returns the players current health as an int.

### Get Velocity

```lua
player:get_velocity()
```

Returns the players velocity as x, y, z. You can pass these values into a vector and call the length method to get the velocity as an int.

### Get User ID

```lua
player:get_user_id()
```

Returns the user id as a number.

### Get Max Health

```lua
player:get_max_health()
```

Returns the players maximum health as a int.

### Bone Position

```lua
player:bone_position(string: bone_name)
```

Returns the position of the specified bone as x, y, z. (Ex. Head)

### Bone Instance

```lua
player:bone_instance(string: bone_name)
```

Returns the instance of the specified bone. (Ex. Head)

### Get Weapon

```lua
player:get_weapon();
```

Returns the players currently held weapon as a string.

### Get Team

```lua
player:get_team();
```

Returns the players team name as a string.

### Get Team Color

```lua
player:get_team_color();
```

Returns the players team color as RGB values, Ex. \[255, 0, 255]&#x20;

## Part Methods:

These can be used on parts IN the **Entity** API to obtain their information.

### Get Position

```lua
part:get_part_position()
```

Returns the position of the specified part as x, y, z.&#x20;

### Get Rotation

```lua
part:get_part_rotation()
```

Returns the rotation of the specified part as matrix3 struct. Here's an example of it's useage:

```lua
-- this script would print the rotation matrix of all parts
    local parts = entity.get_parts()

    for index, part in ipairs(parts) do
        local rotation_matrix = part:get_part_rotation()

        print("Rotation of part " .. index .. ":")
        for i = 1, 9 do
            print(rotation_matrix[i])
        end
    end
```

### Get Size

```lua
part:get_part_size()
```

Returns the size of the specified part as x, y, z. Example Useage:

```lua
cheat.set_callback("update", function()
    local parts = entity.get_parts()

    for index, part in ipairs(parts) do
        local size = vector(part:get_part_size())

        print("Size " .. index .. ":" .. size.x .. " " .. size.y .. " " .. size.z)
    end
end)

```

\


\
---
description: Interact with Roblox.
---

# game

{% hint style="info" %}
When using the Game API the info is returned directly from memory. It's strongly advised that these functions are never called within the paint callback. Calling these functions within the paint callback, may cause unexpected performance drops. Querying the game's memory is expensive, and doing it every frame is not optimal.
{% endhint %}

## Functions:

### Data Model

```lua
game.get_data_model();
```

Returns an instance. Every place is represented by a data model, a hierarchy of objects that describe everything about the place.

### Camera Position

```lua
game.get_camera_pos();
```

returns the position of the camera as x,y, z

### Place ID

```lua
game.get_place_id();
```

Returns the game's place id.

### Get Workspace

```lua
game.get_workspace()
```

Returns an Instance that's class is Workspace.

### Get Players

```lua
game.get_players();
```

Returns an Instance that's class is Players.

### Get Local Player

```lua
game.get_local_player();
```

Returns an instance to the local player.

### Is Focused

```lua
game.is_focused()
```

Returns true if the game is in focus.

### Silent Aim

```lua
silent_aim(float: x, float: y)
```

Takes in the screen position or wherever you want to silent aim. Ex. Player screen position.

### Player Whitelist

```lua
player_whitelist(string: player_name)
```

Whitelists/Unwhitelists the provided player's name.

## Methods:

These can be used on instances, anything in the game is considered an instance automatically. There are different type of instances (ex. Part, MeshPart, Player, Workspace, Camera, etc...)

### Get Address

```lua
instance:get_address()
```

Returns the address of the instance as a string.

### Get Children

```lua
instance:get_children()
```

Returns an array containing all of the instance's direct children.

### Get Attribute

```lua
instance:get_attribute(string: attribute_name)
```

Takes the attribute's name as an argument, it then returns a string of the attribute's value.&#x20;

An attribute of:\
RandomAttribute Melatonin

And a usage of get\_attribute("RandomAttribute"), would return the string "Melatonin".

### Get Descendants

```lua
instance:get_descendants()
```

Returns an array that contains all of the descendants of that object.

### Get Parent

```lua
instance:get_parent()
```

Returns a parent instance to the provided instance.

### Get Class Name

```lua
instance:get_class_name()
```

Returns the class name of instance.

### Get Name

```lua
instance:get_name()
```

Returns the name of the instance.

### Set Name

```lua
instance:set_name(string: name)
```

Sets the name of the instance.

### Find First Child

```lua
instance:find_first_child(string: name)
```

Returns a child instance with the provided name.

### Find First Child Of Class

```lua
instance:find_first_child_of_class(string: class_name)
```

Returns a child instance with the provided class name.

### Get Button Position

```lua
instance:get_button_position()
```

Returns two float values of the position.

### Get Button Size

```lua
instance:get_button_size()
```

Returns two float values of the size of the instance.

### Set Button Size

```lua
instance:set_button_size(vector: size)
```

Takes a vector as an argument and sets the size of the button. The vector should be formatted as (sizex, sizey, 0)

### Is A

```lua
instance:is_a(string: class_name)
```

Returns true if the instance is the compared class name.

### Get Proximity Prompt Hold Duration

```lua
instance:get_hold_duration()
```

Returns float value of the hold duration of a Proximity Prompt.

### Set Proximity Prompt Hold Duration

```lua
instance:set_hold_duration(float: duration)
```

Sets the hold duration of a Proxmity Prompt to a float value.

### Get Proximity Prompt Max Activation Distance

```lua
instance:get_activation_distance()
```

Returns float value of the max activation distance.

### Set Proximity Prompt Max Activation Distance

```lua
instance:set_activation_distance(float: duration)
```

Sets the Max Activation Distance of a Proximity Prompt to a float value.

### Get Proximity Prompt Action Text

```lua
instance:get_proximity_action_text()
```

Returns the Action Text of a proximity prompt as a string.

### Get Proximity Prompt Exclusivity

```lua
instance:get_proximity_exclusivity()
```

Returns a string of the Proximity Prompt's Exclusivity. Example: "OnePerButton" or "AlwaysShow"

### Set Proximity Prompt Exclusivity

```lua
instance:set_proximity_exclusivity(string: exclusivity)
```

Set the exclusivity of a Proximity Prompt. Takes a string such as:\
"OnePerButton"\
"AlwaysShow"\
"OneGlobally"

### Get Frame "Visible" Flag

```lua
instance:get_frame_visible()
```

Returns an int value.  256 = Visible, 0 = Not Visible.

### Get TextLabel Value

```lua
instance:get_textlabel_value()
```

Returns string value from the TextLabel.

### Set TextLabel Value

```lua
instance:set_textlabel_value(string: value)
```

Sets the string value from the TextLabel.

### Get Vector3 Value

```lua
instance:get_vector_value()
```

Returns 3 floats (x, y ,z)

### Set Vector3 Value

```lua
instance:set_vector_value(float: x, float: y, float: z)
```

Sets the value of a Vector3 instance, takes 3 floats as parameters.

### Get String Value

```lua
instance:get_string_value()
```

Returns string value.

### Set String Value

```lua
instance:set_string_value(string)
```

sets the value of the instance to a string.

### Get Number Value

```lua
instance:get_number_value()
```

Returns number value.

### Get Object Value

```lua
instance:get_objectvalue()
```

Returns the instance of whatever object is being held.

### Set Object Value

```lua
instance:set_objectvalue(Instance: object)
```

Sets the instance of whatever object is being held.

### Get Int Value

```lua
instance:get_int_value()
```

Returns integer value.

### Get Bool Value

```lua
instance:get_bool_value()
```

Returns boolean value.

### Set Bool Value

```lua
instance:set_bool_value(bool: value)
```

Sets a boolean value.



### Set Number Value

```lua
instance:set_number_value(double: value)
```

### Set Int Value

```lua
instance:set_int_value(int: value)
```

## Part & Mesh Part Methods:

### Get Position

```lua
instance:get_position()
```

Returns the position as 3 floats

### Set Position

```lua
instance:set_position()
```

Takes in a vector and sets the position of an instance

### Get Bone Position

```lua
instance:get_bone_position()
```

Returns the position of a Bone instance as 3 floats

### Get Velocity

```lua
instance:get_velocity()
```

Returns the velocity as 3 floats.

### Set Velocity

```lua
instance:set_velocity(float: x, float: y, float: z)
```

Sets the velocity of an instance based on 3 floats provided as parameters.

### Get Look Vector

```lua
instance:get_look_vector()
```

Returns the look vector as 3 floats.

### Set Look Vector

```lua
instance:set_look_vector(vector: value)
```

Takes a vector as an argument and writes to the Look Vector inside CFrame

### Get Up Vector

```lua
instance:get_up_vector()
```

Returns the up vector as 3 floats.

### Set Up Vector

```lua
instance:set_up_vector(vector: value)
```

Takes a vector as an argument and writes to the Up Vector inside CFrame

### Get Right Vector

```lua
instance:get_right_vector()
```

Returns the right vector as 3 floats.

### Set Right Vector

```lua
instance:set_right_vector(vector: value)
```

Takes a vector as an argument and writes to the Right  Vector inside CFrame



### Get Color

```lua
instance:get_color()
```

Returns 3 int values (RGB) of the instance's color.

### Set Color

```lua
instance:set_color(int: red, int: green, int: blue)
```

Sets the color of the instance.

### Get Size

```lua
instance:get_size()
```

Returns  x, y, z of  the size of the instance

### Set Size

```lua
instance:set_size(vector: x,y,z)
```

Overrides the instances size with the values passed. Takes a vector

### Get Material

```lua
instance:get_material()
```

Return a string of the Instance's Material. Ex. "ForceField"

### Set Material

```lua
instance:set_material(string: material)
```

Overrides the instances material with the selected material.

For a list of Materials, check here: [https://pastebin.com/cfQFAK0H](https://pastebin.com/cfQFAK0H)

### Get Mesh ID

```lua
instance:get_mesh_id()
```

Returns the mesh id as a string.

### Set Mesh ID

```lua
instance:set_mesh_id(string: mesh)
```

Sets the mesh id of a part.

### Get Texture ID

```lua
instance:get_textureid()
```

Returns the texture id as a string. This is intended for MeshParts.

### Get Decal Texture ID

```lua
instance:get_decal_textureid()
```

Returns the texture id as a string. This is intended for Decals.

### Get SpecialMesh Texture ID

```lua
instance:get_textureid_specialmesh()
```

Returns the texture id as a string. This is intended for SpecialMesh instances.

### Get SoundID

```lua
instance:get_soundid()
```

Returns the soundid as a string.

### Set Highlight Occlusion

```lua
instance:set_highlight_ontop()
```

Sets the instance (a highlight) to AlwaysOnTop.

### Set Highlight Transparency

```lua
instance:set_highlight_transparency(float: value)
```

Takes a float as an arg and sets the instance's tranparency.

### Get Reflectance

```lua
instance:get_reflectance()
```

Returns the reflectance of an instance as a float.

### Set Reflectance

```lua
instance:set_reflectance(float: reflectance)
```

Sets the reflectance of an instance as a float.

### Get Transparency

```lua
instance:get_transparency()
```

Returns the transparency of an instance as a float.

### Set Transparency

```lua
instance:set_transparency(float: transparency)
```

Sets the transparency of an instance.

## Humanoid Methods:

### Get Move Direction

```lua
instance:get_move_direction()
```

Returns the move direction as x, y, z.

### Get Health

```lua
instance:get_health()
```

Returns the health value as a number.

### Get Max Health

```lua
instance:get_max_health()
```

Returns the maximum health value as a number.

## Player Methods:

### Get Character

```lua
instance:get_character()
```

Returns the character of that instance.

### Set Character

```lua
instance:set_character(Instance: character)
```

Sets the character of that instance.

### Get Team

```lua
instance:get_team()
```

Returns the team name.

### Get User ID

```lua
instance:get_user_id()
```

Returns the user id as a number.
---
description: Interact with cheat functions.
---

# utility

## Functions:

### Print/Log

```lua
utility.log(string: message)
```

### Random Int

```lua
utility.random_int(int: min, int: max)
```

Returns a random number between the minimum and maximum as a integer.

### Random Float&#x20;

```lua
utility.random_float(float: min, float: max)
```

Returns a random number between the minimum and maximum as a float.

### World To Screen

```lua
utility.world_to_screen(int: x, int: y, int: z)
```

Converts world coordinates to screen cordinates.

### Key State&#x20;

```lua
utility.key_state(key: key)
```

Returns the state of that key.

### Delta Time

```lua
utility.get_delta_time()
```

Returns the time elapsed since the last frame was rendered.

### Tick Count

```lua
utility.get_tickcount()
```

Returns the number of milliseconds that have elapsed since the system was started.

### Get Fingerprint

```lua
utility.get_fingerprint()
```

Returns a unique fingerprint of the users computer, this can be used to uniquely identify someone's computer.

### Get Clipboard

```lua
utility.get_clipboard()
```

Returns the current contents of the users clipboard.

### Set Clipboard

```lua
utility.set_clipboard(string: content)
```

Set's the users clipboard to whatever content you specify.

### Load Image

```lua
utility.load_image(string: image_file_content)
```

The image file content can be obtained from either a web request, or by using the file system to read the contents and passing it. **This function should be called once per image** as it will return a texture id. This can then be passed into any functions that support a texture id.
---
description: Interact with mouse and keyboard.
---

# input

{% hint style="info" %}
When passing a button to the mouse:

* 1 - Left Mouse
* 2 - Right Mouse
* 3 - Middle Mouse

When passing a key to the keyboard:

[https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes](https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes)
{% endhint %}

## Functions:

### Mouse Move

```lua
input.mouse_move(int: x, int: y)
```

### Mouse Click

```lua
input.mouse_click(int: button)
```

Simulates a click.

### Mouse Down

```lua
input.mouse_down(int: button)
```

Simulates holding a mouse button down.

### Mouse Up

```lua
input.mouse_up(int: button)
```

Simulates lifting a mouse button.

### Cursor Position

```lua
input.cursor_position()
```

Returns the x, y coordinates of the cursor.
---
description: List of valid events that can be passed into the callback system.
---

# events

## Example:

This would execute the code in the function every overlay frame.

```lua
cheat.set_callback("paint", function()
    print("We\'re in the drawing thread!")
end)
```

## Events:

### paint

Fired every overlay frame, used to draw anything.

### update

Fired every few milliseconds, primarily used for caching information.

### update\_slow

Fired every second, primarily used for caching information that is needed less frequently.

### shutdown

Fired when a script is unloaded.

### new\_place

Fired when a new place is loaded.

### config\_save

Fired when a config is saved.

### config\_load

Fired when a config is loaded.

### menu\_toggle

Fired when the menu is open or closed.
