local esp_enabled_ref = ui.new_checkbox("Scripts", "Elements", "Enable ESP")
local target_names_ref = ui.new_input_text("Scripts", "Elements", "Target Name (Folder/Model/Part)")

local box_esp_ref = ui.new_checkbox("Scripts", "Elements", "2D Box")
local box_color_ref = ui.new_colorpicker("Scripts", "Elements", "Box Color", 255, 0, 0, 255, true)
local box_esp_filled_ref = ui.new_checkbox("Scripts", "Elements", "Box Fill")
local box_filled_color_ref = ui.new_colorpicker("Scripts", "Elements", "Filled Color", 255, 0, 0, 65, true)

local name_esp_ref = ui.new_checkbox("Scripts", "Elements", "Name")
local name_color_ref = ui.new_colorpicker("Scripts", "Elements", "Item Name Color", 255, 180, 50, 255, true)
local distance_esp_ref = ui.new_checkbox("Scripts", "Elements", "Distance")
local distance_color_ref = ui.new_colorpicker("Scripts", "Elements", "Distance Color", 255, 180, 50, 255, true)

local headdot_esp_ref = ui.new_checkbox("Scripts", "Elements", "Head Dot")
local headdot_color_ref = ui.new_colorpicker("Scripts", "Elements", "Head Dot Color", 255, 255, 255, 255, true)

local health_bar_ref = ui.new_checkbox("Scripts", "Elements", "Health Bar")
local health_bar_color_ref = ui.new_colorpicker("Scripts", "Elements", "Health Bar Color", 0, 128, 0, 255, true)

local effect_rainbow = ui.new_checkbox("Scripts", "Elements", "RGB Cycle")
local distance_slider_ref = ui.new_slider_int("Scripts", "Elements", "Max Render Distance", 10, 2000, 500)

local tick_count = 0
local esp_render_cache = {}
local part_cache = {}
local cache_expiry = {}
local last_update_time = 0
local last_esp_input = ""
local cached_esp_targets = {}

local function find_all_instances_recursively(parent, name, found_instances, max_depth)
    max_depth = max_depth or 10
    if max_depth <= 0 or not parent then return end
    local success, parent_name = pcall(parent.get_name, parent)
    if success and parent_name == name then table.insert(found_instances, parent) end
    local children_success, children = pcall(parent.get_children, parent)
    if not children_success or not children then return end
    for _, child in pairs(children) do find_all_instances_recursively(child, name, found_instances, max_depth - 1) end
end

local function is_humanoid_alive(model)
    if not model then return true end
    local class_success, class = pcall(model.get_class_name, model)
    if not class_success or class ~= "Model" then return true end
    local humanoid_success, humanoid = pcall(model.find_first_child_of_class, model, "Humanoid")
    if not humanoid_success or not humanoid then return true end
    local health_success, health = pcall(humanoid.get_health, humanoid)
    return health_success and health and health > 0
end

local function get_parts_from_instance(instance)
    local current_time = utility.get_tickcount()
    local cache_key = tostring(instance)
    if part_cache[cache_key] and cache_expiry[cache_key] and current_time - cache_expiry[cache_key] < 5000 then
        return part_cache[cache_key]
    end
    local parts = {}
    if not instance then return parts end
    local class_success, class = pcall(instance.get_class_name, instance)
    if not class_success then return parts end
    if class == "Part" or class == "MeshPart" or class == "BasePart" or class == "UnionOperation" then
        table.insert(parts, instance)
    elseif class == "Model" then
        local children_success, children = pcall(instance.get_descendants, instance)
        if children_success and children then
            for _, child in pairs(children) do
                local child_class_s, child_class = pcall(child.get_class_name, child)
                if child_class_s and (child_class == "Part" or child_class == "MeshPart") then
                    table.insert(parts, child)
                    if #parts >= 20 then break end
                end
            end
        end
    elseif class == "Tool" then
        local handle_success, handle = pcall(instance.find_first_child, instance, "Handle")
        if handle_success and handle then table.insert(parts, handle) end
    end
    part_cache[cache_key], cache_expiry[cache_key] = parts, current_time
    return parts
end

local function get_primary_part_from_instance(instance)
    if not instance then return nil end
    local class_s, class = pcall(instance.get_class_name, instance)
    if class_s and (class == "Part" or class == "MeshPart" or class == "BasePart" or class == "UnionOperation") then
        return instance
    end
    local head_s, head = pcall(instance.find_first_child, instance, "Head")
    if head_s and head then return head end
    local hrp_s, hrp = pcall(instance.find_first_child, instance, "HumanoidRootPart")
    if hrp_s and hrp then return hrp end
    local children_s, children = pcall(instance.get_descendants, instance)
    if children_s and children then
        for _, child in pairs(children) do
            local child_class_s, child_class = pcall(child.get_class_name, child)
            if child_class_s and (child_class == "Part" or child_class == "MeshPart") then return child end
        end
    end
    return nil
end

local function get_bounding_box(parts)
    if not parts or #parts == 0 then return nil, nil, nil end
    local min_vec, max_vec = vector(math.huge, math.huge, math.huge), vector(-math.huge, -math.huge, -math.huge)
    local valid_parts = 0
    for i = 1, #parts do
        local part = parts[i]
        local pos_s, pos_x, pos_y, pos_z = pcall(part.get_position, part)
        local size_s, size_x, size_y, size_z = pcall(part.get_size, part)
        if pos_s and size_s then
            valid_parts = valid_parts + 1
            local half_size = vector(size_x * 0.5, size_y * 0.5, size_z * 0.5)
            local pos_vec = vector(pos_x, pos_y, pos_z)
            min_vec.x, min_vec.y, min_vec.z = math.min(min_vec.x, pos_vec.x - half_size.x), math.min(min_vec.y, pos_vec.y - half_size.y), math.min(min_vec.z, pos_vec.z - half_size.z)
            max_vec.x, max_vec.y, max_vec.z = math.max(max_vec.x, pos_vec.x + half_size.x), math.max(max_vec.y, pos_vec.y + half_size.y), math.max(max_vec.z, pos_vec.z + half_size.z)
        end
    end
    if valid_parts > 0 then return (min_vec + max_vec) * 0.5, min_vec, max_vec end
    return nil, nil, nil
end

local function get_safe_color(color_ref)
    local success, color_data = pcall(ui.get, color_ref)
    if not success or type(color_data) ~= "table" then return 255, 255, 255, 255 end
    local r = color_data.r or color_data[1] or 255
    local g = color_data.g or color_data[2] or 255
    local b = color_data.b or color_data[3] or 255
    local a = color_data.a or color_data[4] or 255
    return math.floor(r), math.floor(g), math.floor(b), math.floor(a)
end

local function world_to_screen_box(min_vec, max_vec)
    if not min_vec or not max_vec then return nil end
    local corners = {
        vector(min_vec.x, min_vec.y, min_vec.z), vector(min_vec.x, min_vec.y, max_vec.z),
        vector(min_vec.x, max_vec.y, min_vec.z), vector(min_vec.x, max_vec.y, max_vec.z),
        vector(max_vec.x, min_vec.y, min_vec.z), vector(max_vec.x, min_vec.y, max_vec.z),
        vector(max_vec.x, max_vec.y, min_vec.z), vector(max_vec.x, max_vec.y, max_vec.z)
    }
    local min_x, min_y, max_x, max_y = math.huge, math.huge, -math.huge, -math.huge
    local valid = false
    for _, corner in pairs(corners) do
        local success, sx, sy = pcall(utility.world_to_screen, corner:unpack())
        if success and sx then
            min_x, min_y = math.min(min_x, sx), math.min(min_y, sy)
            max_x, max_y = math.max(max_x, sx), math.max(max_y, sy)
            valid = true
        end
    end
    if not valid then return nil end
    return min_x, min_y, max_x - min_x, max_y - min_y
end

local function update_caches()
    local current_time = utility.get_tickcount()
    if current_time - last_update_time < 5000 then return end
    last_update_time = current_time
    if #part_cache > 500 then part_cache, cache_expiry = {}, {} end
    local esp_input = ui.get(target_names_ref)
    if esp_input ~= last_esp_input then
        last_esp_input = esp_input
        cached_esp_targets = {}
        if esp_input and esp_input ~= "" then
            local workspace_s, workspace = pcall(game.get_workspace)
            if workspace_s and workspace then
                for name in esp_input:gmatch("([^,]+)") do
                    local trimmed_name = name:match("^%s*(.-)%s*$")
                    if trimmed_name ~= "" then
                        find_all_instances_recursively(workspace, trimmed_name, cached_esp_targets, 8)
                    end
                end
            end
        end
    end
    if ui.get(esp_enabled_ref) then
        local new_render_cache, final_list = {}, {}
        for _, found_obj in pairs(cached_esp_targets) do
            local class_s, class = pcall(found_obj.get_class_name, found_obj)
            if class_s and class == "Folder" then
                local children_s, children = pcall(found_obj.get_children, found_obj)
                if children_s and children then
                    for _, child in pairs(children) do table.insert(final_list, child) end
                end
            else
                table.insert(final_list, found_obj)
            end
        end
        esp_render_cache = {}
        for _, obj in pairs(final_list) do
            local name_s, name = pcall(obj.get_name, obj)
            local primary_part = get_primary_part_from_instance(obj)
            if name_s and primary_part then
                table.insert(esp_render_cache, {instance = obj, name = name, primary_part = primary_part})
            end
        end
    else
        esp_render_cache = {}
    end
end

local function draw_esp()
    if not ui.get(esp_enabled_ref) or #esp_render_cache == 0 then return end
    tick_count = utility.get_tickcount()
    local cam_pos_s, cam_x, cam_y, cam_z = pcall(game.get_camera_pos)
    if not cam_pos_s then return end
    local cam_pos = vector(cam_x, cam_y, cam_z)
    local max_dist_sq = ui.get(distance_slider_ref)^2
    local targets_in_range = {}
    for _, item in pairs(esp_render_cache) do
        local pos_s, p_x, p_y, p_z = pcall(item.primary_part.get_position, item.primary_part)
        if pos_s then
            local dist_sq = (vector(p_x, p_y, p_z) - cam_pos):length_sqr()
            if dist_sq <= max_dist_sq then
                table.insert(targets_in_range, {data = item, distance = dist_sq})
            end
        end
    end
    table.sort(targets_in_range, function(a, b) return a.distance < b.distance end)
    local rendered_count = 0
    for i, target in pairs(targets_in_range) do
        if rendered_count >= 100 then break end
        local item = target.data
        if not is_humanoid_alive(item.instance) then goto continue_render_loop end
        local parts = get_parts_from_instance(item.instance)
        local center, min_vec, max_vec = get_bounding_box(parts)
        if not center then goto continue_render_loop end
        local head_part_s, head_part = pcall(item.instance.find_first_child, item.instance, "Head")
        local x, y, w, h = world_to_screen_box(min_vec, max_vec)
        if not x or w <= 0 or h <= 0 then goto continue_render_loop end
        
        if ui.get(box_esp_ref) then
            if ui.get(box_esp_filled_ref) then
                local r_fill, g_fill, b_fill, a_fill
                if ui.get(effect_rainbow) then
                    local hue = (tick_count * 0.001 + i * 0.3) % 1
                    r_fill, g_fill, b_fill = math.floor(255 * (1 + math.sin(hue * 6.28)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 2.09)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 4.18)) / 2)
                    a_fill = 50
                else
                    r_fill, g_fill, b_fill, a_fill = get_safe_color(box_filled_color_ref)
                end
                render.rect(x, y, w, h, r_fill, g_fill, b_fill, a_fill, 0)
            end
            
            local r, g, b, a
            if ui.get(effect_rainbow) then
                local hue = (tick_count * 0.001 + i * 0.3) % 1
                r, g, b = math.floor(255 * (1 + math.sin(hue * 6.28)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 2.09)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 4.18)) / 2)
                a = 255
            else
                r, g, b, a = get_safe_color(box_color_ref)
            end
            
            render.rect_outline(x - 2, y - 2, w + 4, h + 4, r, g, b, 100, 0)
            render.rect_outline(x - 3, y - 3, w + 6, h + 6, r, g, b, 50, 0)
            render.rect_outline(x, y, w, h, r, g, b, a, 0)
            render.rect_outline(x - 1, y - 1, w + 2, h + 2, 0, 0, 0, 255, 0)
            render.rect_outline(x + 1, y + 1, w - 2, h - 2, 0, 0, 0, 255, 0)
        end
        
        if ui.get(name_esp_ref) then
            local name_anchor_pos = vector(center.x, max_vec.y, center.z)
            local screen_s, sx, sy = pcall(utility.world_to_screen, name_anchor_pos:unpack())
            if screen_s and sx then
                local r, g, b, a
                if ui.get(effect_rainbow) then
                    local hue = (tick_count * 0.001 + i * 0.3) % 1
                    r, g, b = math.floor(255 * (1 + math.sin(hue * 6.28)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 2.09)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 4.18)) / 2)
                    a = 255
                else
                    r, g, b, a = get_safe_color(name_color_ref)
                end
                local name_w, _ = render.measure_text(0, true, item.name)
                render.text(sx - (name_w / 2) + 1, y - 15 + 1, 0, 0, 0, a, 0, true, item.name)
                render.text(sx - (name_w / 2), y - 15, r, g, b, a, 0, true, item.name)
            end
        end

        if ui.get(distance_esp_ref) then
            local dist_val = math.floor(math.sqrt(target.distance))
            local dist_text = "[" .. tostring(dist_val) .. "m]"
            local name_anchor_pos = vector(center.x, max_vec.y, center.z)
            local screen_s, sx, sy = pcall(utility.world_to_screen, name_anchor_pos:unpack())
            if screen_s and sx then
                local r, g, b, a
                if ui.get(effect_rainbow) then
                    local hue = (tick_count * 0.001 + i * 0.3) % 1
                    r, g, b = math.floor(255 * (1 + math.sin(hue * 6.28)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 2.09)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 4.18)) / 2)
                    a = 255
                else
                    r, g, b, a = get_safe_color(distance_color_ref)
                end
                local dist_w, _ = render.measure_text(0, true, dist_text)
                render.text(sx - (dist_w / 2) + 1, y + h + 5 + 1, 0, 0, 0, a, 0, true, dist_text)
                render.text(sx - (dist_w / 2), y + h + 5, r, g, b, a, 0, true, dist_text)
            end
        end

        if ui.get(health_bar_ref) then
            local humanoid_s, humanoid = pcall(item.instance.find_first_child_of_class, item.instance, "Humanoid")
            if humanoid_s and humanoid then
                local hp_s, hp = pcall(humanoid.get_health, humanoid)
                local max_hp_s, max_hp = pcall(humanoid.get_max_health, humanoid)
                if hp_s and max_hp_s and type(hp) == "number" and max_hp > 0 then
                    local health_percent = math.max(0, math.min(1, hp / max_hp))
                    local bar_width, bar_height = 2, h * health_percent
                    local bar_x, bar_y = x - bar_width - 5, y + h - bar_height
                    local r, g, b, a
                    if ui.get(effect_rainbow) then
                        local hue = (tick_count * 0.001 + i * 0.3) % 1
                        r, g, b = math.floor(255 * (1 + math.sin(hue * 6.28)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 2.09)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 4.18)) / 2)
                        a = 255
                    else
                        r, g, b, a = get_safe_color(health_bar_color_ref)
                    end
                    if health_percent > 0 then
                        render.rect_outline(bar_x - 2, bar_y - 2, bar_width + 4, bar_height + 4, r, g, b, 100, 0)
                        render.rect_outline(bar_x - 3, bar_y - 3, bar_width + 6, bar_height + 6, r, g, b, 50, 0)
                        render.rect(bar_x, bar_y, bar_width, bar_height, r, g, b, a, 0)
                        render.rect_outline(bar_x - 1, bar_y, bar_width + 2, bar_height, 0, 0, 0, 150, 0)
                    end
                end
            end
        end
        
        if ui.get(headdot_esp_ref) and head_part_s and head_part then
            local pos_s, hx, hy, hz = pcall(head_part.get_position, head_part)
            if pos_s then
                local screen_s, sx, sy = pcall(utility.world_to_screen, hx, hy, hz)
                if screen_s and sx then
                    local r, g, b, a
                    if ui.get(effect_rainbow) then
                        local hue = (tick_count * 0.001 + i * 0.3) % 1
                        r, g, b = math.floor(255 * (1 + math.sin(hue * 6.28)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 2.09)) / 2), math.floor(255 * (1 + math.sin(hue * 6.28 + 4.18)) / 2)
                        a = 255
                    else
                        r, g, b, a = get_safe_color(headdot_color_ref)
                    end
                    render.circle(sx, sy, 1.5, r, g, b, a, 12)
                end
            end
        end
        
        rendered_count = rendered_count + 1
        ::continue_render_loop::
    end
end

cheat.set_callback("paint", function()
    update_caches()
    draw_esp()
end)